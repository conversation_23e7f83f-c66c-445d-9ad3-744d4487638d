{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\abis2\\android\\app\\.cxx\\RelWithDebInfo\\1n6m76z5\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\abis2\\android\\app\\.cxx\\RelWithDebInfo\\1n6m76z5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}