import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:abis2/api_handling/ItemPriceAPI.dart';

class DayRateReportAPI {
  static const String _baseUrl =
      "https://retailuat.abisaio.com:9001/api/BranchChannelRate/ALL/";

  static Future<List<Map<String, dynamic>>> fetchDayRateReportByChannel(
      String branchID, String binID, String channel, String bearerToken) async {

    final businessDate = await _fetchCurrentBusinessDate(branchID, bearerToken);


    final Uri url = Uri.parse(
        "$_baseUrl$branchID/$businessDate/$channel");

    final response = await http.get(url, headers: {
      "Authorization": "Bearer $bearerToken",
      "Content-Type": "application/json",
    });

    if (response.statusCode == 200) {
      List<dynamic> data = jsonDecode(response.body);
      List<Map<String, dynamic>> finalList = [];

      for (var item in data) {
        double rate = item["rate"] ?? 0;

        if (rate > 0) {
          finalList.add({
            "ItemID": item["itemCode"],
            "ItemName": item["itemName"],
            "CategoryName": item["categoryName"],
            "Rate": rate,
            "Channel": channel,
          });
        }
      }

      return finalList;
    }
    else {
      throw Exception("Failed to load stock data for channel $channel");
    }
  }


  static Future<String?> _fetchCurrentBusinessDate(String branchId, String bearerToken) async {
    final String url = 'https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=$branchId';

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $bearerToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data is List && data.isNotEmpty) {
          final rawDate = data[0]['BusinessDateCode'];
          if (rawDate is String) {
            final date = DateTime.parse(rawDate);
            return '${date.year}${_twoDigits(date.month)}${_twoDigits(date.day)}';
          }
        }
      } else {
        print('Failed to fetch business date: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching business date: $e');
    }

    return null;
  }

  /// Utility to format numbers with leading zero
  static String _twoDigits(int n) => n.toString().padLeft(2, '0');
}