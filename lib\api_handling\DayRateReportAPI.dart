import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:abis2/api_handling/ItemPriceAPI.dart';

class DayRateReportAPI {
  static const String _baseUrl =
      "https://retailuat.abisibg.com/api/v1/fetchstock";

  static Future<List<Map<String, dynamic>>> fetchDayRateReportByChannel(
      String branchID, String binID, String channel, String bearerToken) async {
    final Uri url = Uri.parse(
        "$_baseUrl?BranchID=$branchID&BinId=$binID&ItemStatusID=ALL&ItemCategoryId=ALL&Channel=$channel&ItemID=ALL&BatchNumber=ALL");

    final response = await http.get(url, headers: {
      "Authorization": "Bearer $bearerToken",
      "Content-Type": "application/json",
    });

    if (response.statusCode == 200) {
      List<dynamic> data = jsonDecode(response.body);
      List<Map<String, dynamic>> finalList = [];

      for (var item in data) {
        String itemId = item["ItemID"];
        var priceResult = await ItemPriceAPI.fetchItemPrice(
            branchID, channel, itemId, bearerToken);

        if (priceResult != null && (priceResult['rate'] ?? 0) > 0) {
          finalList.add({
            "ItemID": item["ItemID"],
            "ItemName": item["ItemName"],
            "CategoryName": item["CategoryName"],
            "Rate": priceResult['rate'],
            "Channel": channel,
          });
        }
      }

      return finalList;
    } else {
      throw Exception("Failed to load stock data for channel $channel");
    }
  }
}